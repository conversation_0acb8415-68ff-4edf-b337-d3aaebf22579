@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

.dark {
  --background: #000000;
  --foreground: #ededed;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom scrollbar for dark mode */
.dark::-webkit-scrollbar {
  width: 8px;
}

.dark::-webkit-scrollbar-track {
  background: #000000;
}

.dark::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 4px;
}

.dark::-webkit-scrollbar-thumb:hover {
  background: #555555;
}
