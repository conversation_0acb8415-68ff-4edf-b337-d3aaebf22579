'use client';

import { useState } from 'react';
import { getTokenPriceFromBirdeye, fetchPriceHistoryFromBirdeye } from '@/lib/api';

export default function TestBirdeyePage() {
  const [address, setAddress] = useState('9raUVuzeWUk53co63M4WXLWPWE4Xc6Lpn7RS9dnkpump'); // V2EX token
  const [result, setResult] = useState<any>(null);
  const [historyResult, setHistoryResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    console.log(message);
  };

  const testBirdeyeAPI = async () => {
    if (!address.trim()) {
      addLog('请输入代币地址');
      return;
    }

    setLoading(true);
    setResult(null);
    setHistoryResult(null);
    setLogs([]);
    
    addLog(`开始测试Birdeye API: ${address}`);
    
    try {
      // 测试价格API
      addLog('调用getTokenPriceFromBirdeye...');
      const priceResult = await getTokenPriceFromBirdeye(address.trim());
      addLog(`价格API结果: ${priceResult ? '成功' : '失败'}`);
      
      if (priceResult) {
        addLog(`代币名称: ${priceResult.name}`);
        addLog(`代币符号: ${priceResult.symbol}`);
        addLog(`当前价格: $${priceResult.current_price}`);
        addLog(`24h变化: ${priceResult.price_change_percentage_24h}%`);
        addLog(`市值: $${priceResult.market_cap}`);
        setResult(priceResult);
      }

      // 测试历史价格API
      addLog('调用fetchPriceHistoryFromBirdeye...');
      const historyData = await fetchPriceHistoryFromBirdeye(address.trim(), '24H');
      addLog(`历史价格API结果: ${historyData.length > 0 ? '成功' : '失败'}`);
      
      if (historyData.length > 0) {
        addLog(`获取到 ${historyData.length} 个历史价格点`);
        addLog(`最新价格: $${historyData[historyData.length - 1]?.price || 'N/A'}`);
        setHistoryResult(historyData);
      }

    } catch (error) {
      addLog(`错误: ${error}`);
      console.error('Birdeye API测试失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          Birdeye API 测试
        </h1>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                代币地址
              </label>
              <input
                type="text"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="输入Solana代币地址"
              />
            </div>

            <button
              onClick={testBirdeyeAPI}
              disabled={loading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              {loading ? '测试中...' : '测试Birdeye API'}
            </button>
          </div>
        </div>

        {/* 日志显示 */}
        {logs.length > 0 && (
          <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">执行日志</h3>
            <div className="space-y-1 max-h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="text-sm text-gray-600 dark:text-gray-400 font-mono">
                  {log}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 价格结果显示 */}
        {result && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">价格数据</h3>
            <pre className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-4 rounded overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        {/* 历史价格结果显示 */}
        {historyResult && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              历史价格数据 ({historyResult.length} 个数据点)
            </h3>
            <div className="max-h-64 overflow-y-auto">
              <pre className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-4 rounded">
                {JSON.stringify(historyResult.slice(0, 10), null, 2)}
                {historyResult.length > 10 && '\n... (显示前10个数据点)'}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
