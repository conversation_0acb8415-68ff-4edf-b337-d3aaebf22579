'use client';

import { useState, useEffect, useCallback } from 'react';
import { X, Clock, TrendingUp, TrendingDown } from 'lucide-react';
import { CryptoCurrency, PricePoint } from '@/types/crypto';
import { fetchPriceHistoryFromBirdeye, formatPrice, formatPercentage, formatMarketCap, formatSupply } from '@/lib/api';
import PriceChart from './PriceChart';
import ChartControls from './ChartControls';

interface ManualCoinCardProps {
  crypto: CryptoCurrency;
  onRemove?: (coinId: string) => void;
  onUpdate?: (coinId: string, newData: CryptoCurrency) => void;
  showRemoveButton?: boolean;
}

export default function ManualCoinCard({ crypto, onRemove, onUpdate, showRemoveButton = true }: ManualCoinCardProps) {
  const [tokenIcon, setTokenIcon] = useState<string>('');
  const [chartPeriod, setChartPeriod] = useState('24H');
  const [priceHistory, setPriceHistory] = useState<PricePoint[]>([]);
  const [chartLoading, setChartLoading] = useState(false);
  const coinName = crypto.name;
  const isManualCoin = crypto.id.startsWith('manual-');
  const hasRealData = crypto.current_price > 0;

  // 判断是否为meme币（这里可以根据实际需求调整判断逻辑）
  const isMemeToken = coinName.toLowerCase().includes('meme') ||
                     coinName.toLowerCase().includes('pepe') ||
                     coinName.toLowerCase().includes('doge') ||
                     coinName.toLowerCase().includes('trump') ||
                     coinName.toLowerCase().includes('v2ex') ||
                     crypto.symbol?.toLowerCase().includes('meme') ||
                     crypto.symbol?.toLowerCase().includes('v2ex') ||
                     // 可以根据需要添加更多meme币的判断条件
                     false;

  // 获取价格历史数据
  const loadPriceHistory = useCallback(async (period: string) => {
    // 只有当有真实数据且有dexscreener_data时才加载图表
    if (!hasRealData || !crypto.dexscreener_data?.pairAddress) return;

    setChartLoading(true);
    try {
      // 从代币地址获取历史数据
      const tokenAddress = crypto.id.replace('birdeye-', '').replace('manual-', '');
      if (tokenAddress && tokenAddress !== crypto.id) {
        const history = await fetchPriceHistoryFromBirdeye(tokenAddress, period);
        setPriceHistory(history);
      }
    } catch (error) {
      console.error('Failed to load price history:', error);
      setPriceHistory([]);
    } finally {
      setChartLoading(false);
    }
  }, [hasRealData, crypto.dexscreener_data?.pairAddress, crypto.id]);

  // 处理图表周期变化
  const handlePeriodChange = useCallback((period: string) => {
    setChartPeriod(period);
    loadPriceHistory(period);
  }, [loadPriceHistory]);

  if (!isManualCoin) {
    return null;
  }



  // 初始化图表数据
  useEffect(() => {
    if (hasRealData && crypto.dexscreener_data?.pairAddress) {
      loadPriceHistory(chartPeriod);
    }
  }, [hasRealData, crypto.dexscreener_data?.pairAddress, loadPriceHistory, chartPeriod]);

  // 获取代币图标
  useEffect(() => {
    const fetchTokenIcon = async () => {
      try {
        // 首先检查是否已有图标
        if (crypto.image) {
          setTokenIcon(crypto.image);
          return;
        }

        // 特殊处理一些知名代币
        const specialTokens: { [key: string]: string } = {
          'v2ex': 'https://cdn.v2ex.com/gravatar/c4ca4238a0b923820dcc509a6f75849b?s=64&d=retro',
          'trump': 'https://assets.coingecko.com/coins/images/33441/large/trump.png',
          'pepe': 'https://assets.coingecko.com/coins/images/29850/large/pepe-token.jpeg',
          'doge': 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png',
        };

        const tokenKey = coinName.toLowerCase();
        const symbolKey = (crypto.symbol || '').toLowerCase();

        // 检查币名或符号是否匹配特殊代币
        if (specialTokens[tokenKey] || specialTokens[symbolKey]) {
          const iconUrl = specialTokens[tokenKey] || specialTokens[symbolKey];
          console.log('使用特殊图标:', iconUrl);
          setTokenIcon(iconUrl);
          return;
        }

        // 尝试从多个来源获取图标
        const queries = [coinName, crypto.symbol].filter(Boolean);

        for (const query of queries) {
          try {
            // 使用无需API key的CoinGecko搜索
            const response = await fetch(
              `https://api.coingecko.com/api/v3/search?query=${encodeURIComponent(query)}`,
              {
                headers: {
                  'Accept': 'application/json',
                },
              }
            );

            if (response.ok) {
              const data = await response.json();
              console.log(`CoinGecko搜索 ${query} 结果:`, data);

              if (data.coins && data.coins.length > 0) {
                // 寻找最匹配的币种
                const exactMatch = data.coins.find((c: any) =>
                  c.name.toLowerCase() === query.toLowerCase() ||
                  c.symbol.toLowerCase() === query.toLowerCase()
                );

                const coin = exactMatch || data.coins[0]; // 如果没有精确匹配，使用第一个结果

                if (coin && (coin.thumb || coin.large || coin.small)) {
                  const iconUrl = coin.large || coin.thumb || coin.small;
                  console.log('找到CoinGecko图标:', iconUrl);
                  setTokenIcon(iconUrl);
                  return;
                }
              }
            }
          } catch (error) {
            console.log(`搜索 ${query} 图标失败:`, error);
          }
        }

        // 如果CoinGecko失败，尝试使用通用的加密货币图标服务
        const symbolLower = (crypto.symbol || coinName).toLowerCase();
        const fallbackIcons = [
          `https://cryptoicons.org/api/icon/${symbolLower}/200`,
          `https://assets.coincap.io/assets/icons/${symbolLower}@2x.png`,
          `https://raw.githubusercontent.com/spothq/cryptocurrency-icons/master/128/color/${symbolLower}.png`,
          // 为V2EX添加更多备用选项
          ...(tokenKey === 'v2ex' ? [
            'https://v2ex.com/static/img/<EMAIL>',
            'https://v2ex.com/static/img/icon_rayps_64.png',
            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiMzMzMiLz4KPHRleHQgeD0iMzIiIHk9IjM4IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VjwvdGV4dD4KPC9zdmc+'
          ] : [])
        ];

        for (const iconUrl of fallbackIcons) {
          try {
            const response = await fetch(iconUrl, { method: 'HEAD' });
            if (response.ok) {
              console.log('使用备用图标:', iconUrl);
              setTokenIcon(iconUrl);
              return;
            }
          } catch (error) {
            console.log('备用图标失败:', error);
          }
        }

      } catch (error) {
        console.error('获取代币图标失败:', error);
      }
    };

    if (hasRealData && !tokenIcon) {
      fetchTokenIcon();
    }
  }, [coinName, crypto.symbol, crypto.image, crypto.name, hasRealData, tokenIcon]);







  return (
    <div className="bg-white dark:bg-black rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 relative group">
      {/* Mac风格关闭按钮 */}
      {showRemoveButton && onRemove && (
        <button
          onClick={() => onRemove(crypto.id)}
          className="absolute top-2 right-2 w-5 h-5 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100 z-10 shadow-md group/button"
          title="移除此币种"
        >
          <X className="w-2.5 h-2.5 text-white opacity-0 group-hover/button:opacity-100 transition-opacity duration-200" />
        </button>
      )}

      <div className="space-y-4">
        {/* 币种名称和符号 - 与传统币卡片布局一致 */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                {crypto.symbol || coinName.toUpperCase()}
              </h3>
              {crypto.market_cap_rank && (
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full">
                  #{crypto.market_cap_rank}
                </span>
              )}
              {isMemeToken && (
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs px-2 py-1 rounded-full">
                  MEME
                </span>
              )}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {coinName}
            </p>
          </div>

          {/* 币种图标 */}
          <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
            {tokenIcon ? (
              <img
                src={tokenIcon}
                alt={coinName}
                className="w-10 h-10 object-cover rounded-full"
                onError={() => setTokenIcon('')}
              />
            ) : (
              <span className="text-gray-600 dark:text-gray-300 font-bold text-sm">
                {(crypto.symbol || coinName).charAt(0)}
              </span>
            )}
          </div>
        </div>

        {/* 价格 */}
        <div>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {formatPrice(crypto.current_price)}
          </p>
          {/* 24小时高低价 */}
          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
            <span>高: {formatPrice(crypto.high_24h || crypto.current_price)}</span>
            <span>低: {formatPrice(crypto.low_24h || crypto.current_price)}</span>
          </div>
        </div>

        {/* 价格图表 */}
        {hasRealData && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">价格走势</h4>
              <ChartControls
                selectedPeriod={chartPeriod}
                onPeriodChange={handlePeriodChange}
              />
            </div>

            {chartLoading ? (
              <div className="h-32 flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <PriceChart
                data={priceHistory}
                symbol={crypto.symbol || coinName}
                isPositive={crypto.price_change_percentage_24h >= 0}
                period={chartPeriod}
              />
            )}
          </div>
        )}

        {/* 24h变化和7d变化 */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <span className="text-xs text-gray-600 dark:text-gray-400">24h变化</span>
            <div className={`flex items-center space-x-1 ${
              crypto.price_change_percentage_24h >= 0
                ? 'text-green-600 dark:text-green-400'
                : 'text-red-600 dark:text-red-400'
            }`}>
              {crypto.price_change_percentage_24h >= 0 ? (
                <TrendingUp className="w-3 h-3" />
              ) : (
                <TrendingDown className="w-3 h-3" />
              )}
              <span className="font-medium">
                {formatPercentage(crypto.price_change_percentage_24h)}
              </span>
            </div>
          </div>
          <div className="space-y-1">
            <span className="text-xs text-gray-600 dark:text-gray-400">7d变化</span>
            <div className={`flex items-center space-x-1 ${
              crypto.price_change_percentage_7d >= 0
                ? 'text-green-600 dark:text-green-400'
                : 'text-red-600 dark:text-red-400'
            }`}>
              {crypto.price_change_percentage_7d >= 0 ? (
                <TrendingUp className="w-3 h-3" />
              ) : (
                <TrendingDown className="w-3 h-3" />
              )}
              <span className="font-medium">
                {formatPercentage(crypto.price_change_percentage_7d)}
              </span>
            </div>
          </div>
        </div>

        {/* 市值和交易量 */}
        <div className="space-y-2 pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-600 dark:text-gray-400">市值</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {formatMarketCap(crypto.market_cap)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-600 dark:text-gray-400">24h交易量</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {formatMarketCap(crypto.total_volume)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-600 dark:text-gray-400">流通供应量</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {formatSupply(crypto.circulating_supply)}
            </span>
          </div>
          {/* Meme币额外信息 */}
          {isMemeToken && crypto.dexscreener_data && (
            <>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600 dark:text-gray-400">流动性</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {crypto.dexscreener_data.liquidity ? formatMarketCap(crypto.dexscreener_data.liquidity) : '--'}
                </span>
              </div>
              {crypto.dexscreener_data.txns?.h24 && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600 dark:text-gray-400">24h交易次数</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {(crypto.dexscreener_data.txns.h24.buys || 0) + (crypto.dexscreener_data.txns.h24.sells || 0)}
                  </span>
                </div>
              )}
            </>
          )}
        </div>

        {/* 刷新时间显示 */}
        <div className="pt-3 border-t border-gray-100 dark:border-gray-800 mt-4">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>
                更新于 {new Date(crypto.last_updated).toLocaleTimeString('zh-CN', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                })}
              </span>
            </div>

            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${
                isMemeToken ? 'bg-orange-500 animate-pulse' : 'bg-blue-500'
              }`}></div>
              <span>{isMemeToken ? 'Meme币' : '实时数据'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
