# 🚀 部署检查清单

在部署 CryptoTrack 项目之前，请确保完成以下步骤：

## ✅ 部署前检查

### 1. 环境变量配置
- [ ] 已获取 CoinGecko API 密钥
- [ ] 在部署平台配置了 `NEXT_PUBLIC_COINGECKO_API_KEY` 环境变量
- [ ] API 密钥格式正确（以 `CG-` 开头）
- [ ] 已测试 API 密钥有效性

### 2. 代码准备
- [ ] 代码已推送到 Git 仓库
- [ ] 所有依赖已正确安装 (`npm install`)
- [ ] 项目可以正常构建 (`npm run build`)
- [ ] 通过了代码检查 (`npm run lint`)
- [ ] 本地测试通过

### 3. 平台特定配置

#### Vercel 部署
- [ ] 项目已连接到 GitHub 仓库
- [ ] 在 Vercel 项目设置中添加了环境变量
- [ ] 选择了正确的框架预设 (Next.js)
- [ ] 构建命令设置为 `npm run build`
- [ ] 输出目录设置为 `.next`

#### Netlify 部署
- [ ] 构建命令设置为 `npm run build && npm run export`
- [ ] 发布目录设置为 `out`
- [ ] 在站点设置中添加了环境变量

#### Railway 部署
- [ ] 在项目变量中添加了 API 密钥
- [ ] 确认 Node.js 版本兼容性

## 🔧 部署后验证

### 1. 功能测试
- [ ] 网站可以正常访问
- [ ] 加密货币价格正常显示
- [ ] 搜索功能正常工作
- [ ] 添加/删除币种功能正常
- [ ] 深色/浅色主题切换正常
- [ ] 响应式设计在不同设备上正常

### 2. 性能检查
- [ ] 页面加载速度正常
- [ ] API 请求响应正常
- [ ] 没有控制台错误
- [ ] 图片和资源正常加载

### 3. API 状态检查
- [ ] API 状态组件显示正常（如果有配置问题会显示警告）
- [ ] 价格数据实时更新
- [ ] 历史价格图表正常显示

## 🐛 常见问题排查

### API 相关问题

**问题**: 显示 "API 密钥未配置" 错误
- 检查环境变量名称是否正确
- 确认 API 密钥值已正确设置
- 重新部署项目

**问题**: 显示 "API 密钥无效" 错误
- 验证 API 密钥是否有效
- 检查 CoinGecko 账户状态
- 重新生成 API 密钥

**问题**: 显示 "HTTP 429" 错误
- API 请求频率过高
- 等待限制重置
- 考虑升级 API 计划

### 部署相关问题

**问题**: 构建失败
- 检查 Node.js 版本兼容性
- 确认所有依赖已正确安装
- 查看构建日志中的具体错误

**问题**: 环境变量不生效
- 确认变量名拼写正确
- 检查是否需要重新部署
- 验证平台特定的环境变量配置方法

## 📞 获取帮助

如果遇到问题：

1. 查看 [API_SETUP_GUIDE.md](./API_SETUP_GUIDE.md) 获取详细的 API 配置说明
2. 查看 [DEPLOYMENT.md](./DEPLOYMENT.md) 获取完整的部署指南
3. 运行 `npm run check-env` 检查本地环境配置
4. 查看项目的 [Issues 页面](https://github.com/SUNSIR007/cryptoTrack/issues)
5. 创建新的 Issue 描述你的问题

## 🎉 部署成功

恭喜！如果所有检查项都已完成，你的 CryptoTrack 应用应该已经成功部署并正常运行。

记得：
- 定期检查 API 使用情况
- 关注项目更新
- 考虑为项目做贡献

---

**提示**: 将此清单保存为书签，每次部署时都可以参考。
