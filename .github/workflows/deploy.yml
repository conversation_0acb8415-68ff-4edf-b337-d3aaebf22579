name: Build and Test

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Check environment variables
      run: |
        echo "检查环境变量配置..."
        if [ -z "$NEXT_PUBLIC_COINGECKO_API_KEY" ]; then
          echo "⚠️  警告: NEXT_PUBLIC_COINGECKO_API_KEY 未设置"
          echo "请在 Vercel 环境变量中配置 API 密钥"
          echo "这不会影响构建，但部署后需要配置才能正常工作"
        else
          echo "✅ API 密钥已配置"
        fi
      env:
        NEXT_PUBLIC_COINGECKO_API_KEY: ${{ secrets.NEXT_PUBLIC_COINGECKO_API_KEY }}

    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_COINGECKO_API_KEY: ${{ secrets.NEXT_PUBLIC_COINGECKO_API_KEY }}

    - name: Run linting
      run: npm run lint

    - name: Run environment check
      run: npm run check-env
      env:
        NEXT_PUBLIC_COINGECKO_API_KEY: ${{ secrets.NEXT_PUBLIC_COINGECKO_API_KEY }}
