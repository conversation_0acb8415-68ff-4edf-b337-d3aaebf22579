# ===========================================
# CryptoTrack 环境变量配置
# ===========================================

# CoinGecko API 配置 (必需)
# 获取免费API密钥: https://www.coingecko.com/en/api
# 注意：使用 NEXT_PUBLIC_ 前缀使其在客户端可用
NEXT_PUBLIC_COINGECKO_API_KEY=your_coingecko_api_key_here

# 或者使用服务端环境变量 (推荐用于生产环境)
COINGECKO_API_KEY=your_coingecko_api_key_here

# 应用配置 (可选)
NEXT_PUBLIC_APP_NAME=CryptoTrack
NEXT_PUBLIC_APP_VERSION=1.0.0

# ===========================================
# 部署说明
# ===========================================
#
# 本地开发:
# 1. 复制此文件为 .env.local
# 2. 填入你的 CoinGecko API 密钥
# 3. 运行 npm run dev
#
# Vercel 部署:
# 1. 在 Vercel 项目设置中添加环境变量
# 2. 变量名: NEXT_PUBLIC_COINGECKO_API_KEY
# 3. 变量值: 你的 CoinGecko API 密钥
#
# 其他平台部署:
# 根据平台要求设置相应的环境变量
# ===========================================
